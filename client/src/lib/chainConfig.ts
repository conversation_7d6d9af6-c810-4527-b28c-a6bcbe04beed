import {
  define<PERSON>hai<PERSON>,
  get<PERSON>hainMetadata,
  getRpcUrl<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "thirdweb/chains";
import { createThirdwebClient } from "thirdweb";
import type { Chain } from "thirdweb/chains";

// Create thirdweb client
export const client = createThirdwebClient({
  clientId: process.env.NEXT_PUBLIC_THIRDWEB_CLIENT_ID!,
});

// Define supported chains using thirdweb's defineChain
export const supportedChains: Chain[] = [
  defineChain(1), // Ethereum Mainnet
  defineChain(137), // Polygon Mainnet
  defineChain(56), // BNB Smart Chain
  defineChain(11155111), // Sepolia Testnet
  defineChain(80002), // Polygon Amoy Testnet
  defineChain(97), // BSC Testnet
];

// Legacy chain colors for UI consistency (can be removed later)
export const chainColors: Record<number, string> = {
  1: "#627EEA", // Ethereum
  137: "#8247E5", // Polygon
  56: "#F3BA2F", // BNB Smart Chain
  11155111: "#627EEA", // Sepolia
  80002: "#8247E5", // Polygon Amoy
  97: "#F3BA2F", // BSC Testnet
};

// Get chain by chain ID
export const getChainById = (chainId: number): Chain | undefined => {
  return supportedChains.find((chain) => chain.id === chainId);
};

// Get chain by name (async since we need to fetch metadata)
export const getChainByName = async (
  name: string
): Promise<Chain | undefined> => {
  for (const chain of supportedChains) {
    try {
      const metadata = await getChainMetadata(chain);
      if (metadata.name.toLowerCase() === name.toLowerCase()) {
        return chain;
      }
    } catch (error) {
      console.warn(`Failed to get metadata for chain ${chain.id}:`, error);
    }
  }
  return undefined;
};

// Block Explorer Configuration using thirdweb metadata
export interface BlockExplorer {
  name: string;
  url: string;
  apiUrl?: string;
}

// Get block explorers using thirdweb's chain metadata
export const getBlockExplorers = async (
  chainId: number
): Promise<BlockExplorer[]> => {
  const chain = getChainById(chainId);
  if (!chain) {
    return [{ name: "Etherscan", url: "https://etherscan.io" }];
  }

  try {
    const metadata = await getChainMetadata(chain);
    if (metadata.explorers && metadata.explorers.length > 0) {
      return metadata.explorers.map((explorer) => ({
        name: explorer.name,
        url: explorer.url,
        apiUrl: explorer.apiUrl,
      }));
    }
  } catch (error) {
    console.warn(`Failed to get explorers for chain ${chainId}:`, error);
  }

  // Fallback to default
  return [{ name: "Etherscan", url: "https://etherscan.io" }];
};

// Generate explorer URLs for different types of data
export const getExplorerUrl = async (
  chainId: number,
  type: "address" | "tx" | "block" | "token",
  value: string
): Promise<string> => {
  const explorers = await getBlockExplorers(chainId);
  const baseUrl = explorers[0]?.url || "https://etherscan.io";

  switch (type) {
    case "address":
      return `${baseUrl}/address/${value}`;
    case "tx":
      return `${baseUrl}/tx/${value}`;
    case "block":
      return `${baseUrl}/block/${value}`;
    case "token":
      return `${baseUrl}/token/${value}`;
    default:
      return baseUrl;
  }
};

// Helper function to get chain metadata with caching
const chainMetadataCache = new Map<number, any>();

export const getCachedChainMetadata = async (chain: Chain) => {
  if (chainMetadataCache.has(chain.id)) {
    return chainMetadataCache.get(chain.id);
  }

  try {
    const metadata = await getChainMetadata(chain);
    chainMetadataCache.set(chain.id, metadata);
    return metadata;
  } catch (error) {
    console.warn(`Failed to get metadata for chain ${chain.id}:`, error);
    return null;
  }
};

// Helper function to get RPC URL for a chain
export const getChainRpcUrl = (chain: Chain): string => {
  try {
    return getRpcUrlForChain({ chain, client });
  } catch (error) {
    console.warn(`Failed to get RPC URL for chain ${chain.id}:`, error);
    // Fallback RPC URLs
    const fallbackRpcs: Record<number, string> = {
      1: "https://ethereum.rpc.thirdweb.com",
      137: "https://polygon.rpc.thirdweb.com",
      56: "https://binance.rpc.thirdweb.com",
      11155111: "https://sepolia.rpc.thirdweb.com",
      80002: "https://amoy.rpc.thirdweb.com",
      97: "https://bsc-testnet.rpc.thirdweb.com",
    };
    return fallbackRpcs[chain.id] || "https://ethereum.rpc.thirdweb.com";
  }
};
